import os

AIService_API_HOST = os.getenv(
    "AIService_API_HOST", "http://*************"
)  # AI 服务地址
AIService_API_Port_Stream = os.getenv(
    "AIService_API_Port_Stream", "8999"
)  # AI 服务地址
AIService_API_Port_Image_Detection = os.getenv(
    "AIService_API_Port_Image_Detection", "9002"
)  # AI 服务地址
AIService_API_Port_Image_Weighting = os.getenv(
    "AIService_API_Port_Image_Weighting", "9003"
)  # AI 服务地址

# 系统配置
SRS_SERVER_URL = os.getenv(
    "SRS_SERVER_URL", "http://127.0.0.1"
)  # SRS服务器地址 #***********
SRS_API_PORT = os.getenv("SRS_API_PORT", "1985")  # SRS API端口
SRS_RTMP_PORT = os.getenv("SRS_RTMP_PORT", "1935")  # SRS RTMP端口
SRS_HTTP_PORT = os.getenv("SRS_HTTP_PORT", "8080")  # SRS HTTP端口
SRS_WEBRTC_PORT = os.getenv("SRS_WEBRTC_PORT", "8000")  # SRS WebRTC端口

# 视频处理配置
VIDEO_WIDTH = int(os.getenv("VIDEO_WIDTH", "1280"))  # 视频宽度
VIDEO_HEIGHT = int(os.getenv("VIDEO_HEIGHT", "720"))  # 视频高度
VIDEO_FPS = int(os.getenv("VIDEO_FPS", "24"))  # 视频帧率
VIDEO_BITRATE = os.getenv("VIDEO_BITRATE", "2000k")  # 视频码率

# 文件存储配置
UPLOAD_DIR = os.getenv("UPLOAD_DIR", "./uploads")  # 上传文件目录
TEMP_DIR = os.getenv("TEMP_DIR", "./temp")  # 临时文件目录
MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", "500"))  # 最大文件大小(MB)

# WebRTC配置
WEBRTC_ICE_SERVERS = os.getenv(
    "WEBRTC_ICE_SERVERS", "stun:stun.l.google.com:19302"
).split(
    ","
)  # ICE服务器

# 检测配置
DETECTION_CONFIDENCE = float(os.getenv("DETECTION_CONFIDENCE", "0.5"))  # 检测置信度阈值
DETECTION_MODELS = {  # 检测模型配置
    "passage_pig_count": {
        "model_path": "models/line_crossing.pth",
        "input_size": (640, 640),
    },
    "inbar_video": {
        "model_path": "models/area_monitoring.pth",
        "input_size": (640, 640),
    },
    "inbar_image": {
        "model_path": "models/area_monitoring.pth",
        "input_size": (640, 640),
    },
}

# API配置
API_HOST = os.getenv("API_HOST", "0.0.0.0")  # API主机
API_PORT = int(os.getenv("API_PORT", "8000"))  # API端口
# CORS_ORIGINS = os.getenv("CORS_ORIGINS", "http://localhost:3000").split(",") # CORS允许的源
# # config.py

CORS_ORIGINS = [
    "http://localhost:3000",  # 前端本地调试
    "http://127.0.0.1:3000",  # 有些框架用的是 127.0.0.1
    "http://127.0.0.1:3002",
    "http://localhost:3002",
    "http://localhost:3001",  # 前端本地调试
    "http://127.0.0.1:3001",
    "http://localhost:30001",  # 前端本地调试
    "http://127.0.0.1:30001",
    "http://*************:3000",
]

# 日志配置
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")  # 日志级别
LOG_FILE = os.getenv("LOG_FILE", "./logs/app.log")  # 日志文件

# 流配置
STREAM_PREFIX = "cv_stream"  # 流名称前缀
STREAM_TIMEOUT = int(os.getenv("STREAM_TIMEOUT", "30"))  # 流超时时间(秒)

# 确保目录存在
for dir_path in [UPLOAD_DIR, TEMP_DIR, os.path.dirname(LOG_FILE)]:
    os.makedirs(dir_path, exist_ok=True)

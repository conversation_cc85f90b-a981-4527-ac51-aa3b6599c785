import asyncio
import json
import logging
import os  # 用于路径检查
import threading
import time
from typing import Dict, List, Optional, Union

import aiohttp
import cv2
import ffmpeg  # 导入 ffmpeg-python 库
from fastapi import HTTPException

from config import TEMP_DIR, VIDEO_FPS
from core.models import AreaPoint, ImageAiServiceRequest, VideoAiServiceRequest

logger = logging.getLogger(__name__)


class VideoProcessor:
    def __init__(self, ai_func):
        self.active_streams: Dict[str, Dict] = {}
        self.processing_threads: Dict[str, threading.Thread] = {}
        self.stop_flags: Dict[str, bool] = {}
        self._ai_func = ai_func
        # 确保TEMP_DIR存在
        os.makedirs(TEMP_DIR, exist_ok=True)

    # def start_analysis(self, session_id: str, mode: str, input_source: str,
    #                    direction: Optional[str] = None, areas: Optional[List] = None) -> Dict:
    #     """启动视频分析"""
    #     try:
    #         # 创建流ID
    #         stream_id_original = f"original_{session_id}"
    #         stream_id_processed = f"processed_{session_id}"
    #
    #         # 初始化停止标志
    #         self.stop_flags[session_id] = False
    #
    #
    #
    #         # 启动处理线程
    #         thread = threading.Thread(
    #             target=self._process_video,
    #             args=(session_id, mode, input_source, direction, areas, stream_id_original, stream_id_processed)
    #         )
    #         thread.daemon = True  # 设置为守护线程，主程序退出时自动终止
    #         thread.start()
    #
    #         self.processing_threads[session_id] = thread
    #         self.active_streams[session_id] = {
    #             "mode": mode,
    #             "stream_id_original": stream_id_original,
    #             "stream_id_processed": stream_id_processed,
    #             "status": "running"
    #         }
    #
    #         logger.info(f"启动分析成功: {session_id}")
    #         # 注意：这里SRS的地址可能需要配置。这里使用一个假设的地址
    #         return {
    #             "success": True,
    #             "stream_urls": {
    #                 "original": f"http://172.20.10.3:8080/live/{stream_id_original}.flv",  # 示例SRS FLV播放地址
    #                 "processed": f"http://172.20.10.3:8080/live/{stream_id_processed}.flv"  # 示例SRS FLV播放地址
    #             }
    #         }
    #     except Exception as e:
    #         logger.error(f"启动分析失败: {e}", exc_info=True)
    #         return {"success": False, "message": str(e)}

    # def stop_analysis(self, session_id: str) -> bool:
    #     """停止视频分析"""
    #     try:
    #         if session_id in self.stop_flags:
    #             self.stop_flags[session_id] = True
    #
    #         if session_id in self.processing_threads:
    #             # 给线程一点时间来停止
    #             self.processing_threads[session_id].join(timeout=5)
    #             # 如果线程仍然活跃，尝试终止其FFmpeg子进程
    #             if self.processing_threads[session_id].is_alive():
    #                 # 这里需要更精细的FFmpeg进程管理，例如在_start_ffmpeg_stream中存储process对象
    #                 # 并在这里调用process.terminate()。目前的实现需要改进FFmpeg进程的引用。
    #                 # 为了演示目的，我们假设join()足够了，或者FFmpeg进程会被后续的垃圾回收或系统清理。
    #                 logger.warning(f"会话 {session_id} 的处理线程未能正常终止，尝试强制清理FFmpeg进程。")
    #             del self.processing_threads[session_id]
    #
    #         if session_id in self.active_streams:
    #             del self.active_streams[session_id]
    #
    #         logger.info(f"停止分析成功: {session_id}")
    #         return True
    #     except Exception as e:
    #         logger.error(f"停止分析失败: {e}", exc_info=True)
    #         return False

    # 将 start_analysis 变为异步函数，因为它将 await 外部 AI 服务的调用
    async def start_analysis(
        self,
        session_id: str,
        mode: str,
        input_source: str,
        ref_coord: Optional[list] = None,
        resolution: Optional[list] = None,
        direction: Optional[str] = None,
        areas: Optional[List[AreaPoint]] = None,
    ) -> Dict:
        """
        启动视频分析，包括与外部AI服务的交互。
        Args:
            session_id (str): 当前会话的唯一ID。
            mode (str): 分析模式（例如 '出入栏', '圈内（视频）'）。
            input_source (str): 原始视频源的URL（例如 RTMP）。
            direction (Optional[str]): 出入栏模式的方向。
            areas (Optional[List[AreaPoint]]): 圈内模式的区域定义。
        Returns:
            Dict: 包含成功状态和流URL的字典。
        Raises:
            HTTPException: 如果调用外部AI服务失败或返回无效响应。
        """
        # ref_coord: [width, height] height/height
        areas_new = []
        areas_final = []
        if input_source.startswith(("http://", "https://", "rtmp://", "rtsp://")):
            inde = 1
        else:
            inde = 1.5
        if areas is not None:
            if isinstance(ref_coord, list):
                pass
            else:
                ref_coord = json.loads(ref_coord)
                resolution = json.loads(resolution)
            sigma = resolution[-1] / ref_coord[-1]  # TODO check
            for area in areas:
                if isinstance(area, AreaPoint):
                    for a in area.area:
                        areas_new.append(
                            [int(a[0] * sigma / inde), int(a[1] * sigma / inde)]
                        )
                    areas_final.append({"area": areas_new, "area_id": area.area_id})
                else:
                    for a in area["area"]:
                        areas_new.append(
                            [int(a[0] * sigma / inde), int(a[1] * sigma / inde)]
                        )
                    areas_final.append({"area": areas_new, "area_id": area["area_id"]})
        try:
            sid = f"original_{session_id}"
            push_url = f"rtmp://*************/live/{sid}"
            play_url = f"http://*************:8080/live/{sid}.flv"
            self.stop_flags[session_id] = False
            # 先启动推流线程
            ready = threading.Event()

            def push_wrapper(*a, **kw):
                ready.set()
                self._push_stream(*a, **kw)

            t = threading.Thread(
                target=push_wrapper, args=(session_id, input_source, push_url)
            )
            t.daemon = True
            t.start()
            ready.wait(timeout=3)  # 等待推流进程已启动
            self.processing_threads[session_id] = t
            # 再调用AI服务
            if mode == "inbar_video":
                mode = "testmodel"
            if direction is not None:
                req = VideoAiServiceRequest(
                    stream_id=session_id,
                    input_url=push_url,
                    models=[mode],
                    direction=direction,
                )
            else:  # if areas is not None:
                req = VideoAiServiceRequest(
                    stream_id=session_id,
                    input_url=push_url,
                    models=[mode],
                    area_monitor=areas_final,
                )
            logger.info(f"会话{session_id}: 调用AI服务 {push_url}")
            ai_resp = await self._ai_func(req, "video")
            if not ai_resp or not ai_resp.get("output_url"):
                raise HTTPException(status_code=500, detail="AI服务未返回output_url")
            logger.info(f"调用ai视频服务成功： {ai_resp.get('output_url')}")
            self.active_streams[session_id] = {
                "mode": mode,
                "stream_id_original": sid,
                "srs_push_url_original": push_url,
                "external_ai_output_url": ai_resp["output_url"],
                "status": "running",
            }
            logger.info(f"会话{session_id}: 推流线程启动")
            process_id = ai_resp["output_url"].split("/")[-1]
            process_url = f"http://*************:8080/live/{process_id}.flv"
            logger.info(f"original {play_url}; processed {process_url}")
            play_url = f"http://*************:8080/live/{process_id}1.flv"
            return {
                "success": True,
                "message": "视频分析启动成功",
                "stream_urls": {"original": play_url, "processed": process_url},
                "session_id": session_id,
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"启动分析失败:{e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"启动分析失败:{e}")

    # 保持 stop_analysis 为异步，以防它将来需要 await
    async def stop_analysis(self, session_id: str) -> bool:
        """停止视频分析"""
        try:
            if session_id in self.stop_flags:
                self.stop_flags[session_id] = True
            if session_id in self.processing_threads:
                self.processing_threads[session_id].join(timeout=5)
                if self.processing_threads[session_id].is_alive():
                    logger.warning(f"会话{session_id}线程未终止")
                del self.processing_threads[session_id]
            if session_id in self.active_streams:
                del self.active_streams[session_id]
            logger.info(f"停止分析:{session_id}")
            return True
        except Exception as e:
            logger.error(f"停止分析失败:{e}", exc_info=True)
            return False

    def _push_stream(self, session_id: str, input_source: str, push_url: str):
        cap = None
        proc = None
        try:
            # 判断是否为流视频地址，直接推流
            if input_source.startswith(("http://", "https://", "rtmp://", "rtsp://")):
                proc = self._start_ffmpeg_stream(
                    push_url, 0, 0, 0, direct_push=True, input_url=input_source
                )
                if not proc:
                    self.stop_flags[session_id] = True
                    return
                while not self.stop_flags.get(session_id, False):
                    if proc.poll() is not None:
                        self.stop_flags[session_id] = True
                        break
                    time.sleep(1)
            else:
                # 本地文件处理
                cap = cv2.VideoCapture(input_source)
                if not cap.isOpened():
                    time.sleep(1)
                    cap = cv2.VideoCapture(input_source)
                if not cap.isOpened():
                    self.stop_flags[session_id] = True
                    return
                w, h, fps = (
                    int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
                    int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                    cap.get(cv2.CAP_PROP_FPS),
                )
                # 自动规格化
                if abs(w / h - 16 / 9) < 0.01:
                    tw, th = int(w / 1.5), int(h / 1.5)
                elif abs(w / h - 1) < 0.01:
                    tw, th = int(w / 1.5), int(h / 1.5)
                else:
                    tw, th = int(w / 1.5), int(h / 1.5)
                tfps = VIDEO_FPS
                proc = self._start_ffmpeg_stream(
                    push_url, tw, th, tfps, direct_push=False
                )
                if not proc:
                    self.stop_flags[session_id] = True
                    return
                fc, st = 0, time.time()
                while not self.stop_flags.get(session_id, False):
                    ret, frame = cap.read()
                    if not ret:
                        break
                    if (frame.shape[1], frame.shape[0]) != (tw, th):
                        frame = cv2.resize(frame, (tw, th))
                    if proc.poll() is None:
                        self._write_frame_to_ffmpeg(frame, proc)
                    else:
                        self.stop_flags[session_id] = True
                        break
                    fc += 1
                    el = time.time() - st
                    et = fc / tfps
                    if el < et:
                        time.sleep(et - el)
            logger.info(f"会话{session_id}: 推流结束")
        except Exception as e:
            logger.error(f"会话{session_id}: 推流异常:{e}", exc_info=True)
        finally:
            if cap:
                cap.release()
            if proc:
                try:
                    proc.stdin.close()
                    proc.wait(timeout=5)
                    proc.terminate()
                    proc.wait(timeout=2)
                except Exception as ex:
                    logger.error(
                        f"会话{session_id}: 清理FFmpeg错误:{ex}", exc_info=True
                    )
            logger.info(f"会话{session_id}: 资源清理完成")

    def _start_ffmpeg_stream(
        self, rtmp_url, w, h, fps, direct_push: bool, input_url: str = None
    ):
        try:
            if direct_push:
                inp = ffmpeg.input(input_url)
                out = ffmpeg.output(
                    inp, rtmp_url, vcodec="copy", f="flv"
                )  # 直接复制流，不重新编码
            else:
                inp = ffmpeg.input(
                    "pipe:",
                    format="rawvideo",
                    pix_fmt="bgr24",
                    s=f"{w}x{h}",
                    r=str(fps),
                )
                out = ffmpeg.output(
                    inp, rtmp_url, vcodec="libx264", tune="zerolatency", f="flv"
                )
            p = ffmpeg.run_async(
                out, pipe_stdin=not direct_push, quiet=True, overwrite_output=True
            )
            logger.info(
                f"FFmpeg推流:{rtmp_url}({'direct' if direct_push else f'{w}x{h},{fps}'})"
            )
            return p
        except Exception as e:
            logger.error(f"FFmpeg推流失败:{e}", exc_info=True)
            return None

    def _write_frame_to_ffmpeg(self, frame, proc):
        try:
            if proc.stdin:
                proc.stdin.write(frame.tobytes())
        except Exception as e:
            logger.error(f"写入FFmpeg失败:{e}", exc_info=True)

    def get_active_streams(self) -> Dict:
        """获取活跃流信息"""
        return self.active_streams.copy()


async def request_from_ai_service(
    request_data: Union[VideoAiServiceRequest, ImageAiServiceRequest], service_type: str
):
    AIService_API_HOST = "http://*************:8999"
    if service_type == "video":
        url = f"{AIService_API_HOST}/stream/start"
    elif service_type == "image":
        url = f"{AIService_API_HOST}/picture/pig_count_detection"
    else:
        raise ValueError(
            f"不支持的 AI 服务类型: {service_type}. 必须是 'video' 或 'image'."
        )
    try:
        async with aiohttp.ClientSession() as session:
            payload = (
                request_data.model_dump()
                if hasattr(request_data, "model_dump")
                else request_data
            )
            async with session.post(url, json=payload, timeout=60) as resp:
                text = await resp.text()
                try:
                    import json

                    return json.loads(text)
                except Exception:
                    return {"error": text}
    except asyncio.TimeoutError:
        raise HTTPException(status_code=504, detail=f"AI服务响应超时: {url}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI服务连接失败: {e}")

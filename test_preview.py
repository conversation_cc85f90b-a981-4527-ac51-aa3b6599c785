#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频预览接口
"""
import requests
import json
import base64
from PIL import Image
import io
import time

def test_video_preview(video_url, test_name="默认测试"):
    """测试视频预览接口"""
    url = "http://localhost:8000/api/video_url/preview"
    
    payload = {
        "video_url": video_url
    }
    
    try:
        print(f"\n🔍 {test_name}")
        print(f"视频URL: {video_url}")
        
        start_time = time.time()
        response = requests.post(url, json=payload, timeout=30)
        end_time = time.time()
        
        print(f"响应时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 接口调用成功!")
            print(f"分辨率: {result['resolution']['width']} x {result['resolution']['height']}")
            print(f"帧率: {result['fps']}")
            print(f"图片大小: {len(result['preview_image'])} 字符")
            
            # 保存预览图片
            if result['preview_image']:
                filename = f"preview_{test_name.replace(' ', '_')}.jpg"
                image_data = base64.b64decode(result['preview_image'])
                image = Image.open(io.BytesIO(image_data))
                image.save(filename)
                print(f"✅ 预览图片已保存为 {filename}")
                
        else:
            print(f"❌ 接口调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_multiple_formats():
    """测试多种视频流格式"""
    test_cases = [
        {
            "url": "rtmp://*************/live/test_stream",
            "name": "RTMP流测试"
        },
        {
            "url": "rtsp://*************:554/live/test_stream",
            "name": "RTSP流测试"
        },
        {
            "url": "http://*************:8080/live/test_stream.flv",
            "name": "HTTP-FLV流测试"
        },
        {
            "url": "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
            "name": "HTTP视频文件测试"
        }
    ]
    
    print("🚀 开始测试视频预览接口")
    print("=" * 50)
    
    for test_case in test_cases:
        test_video_preview(test_case["url"], test_case["name"])
        time.sleep(1)  # 避免请求过于频繁
    
    print("\n" + "=" * 50)
    print("🎉 所有测试完成!")

def test_error_cases():
    """测试错误情况"""
    print("\n🔍 测试错误情况")
    print("=" * 30)
    
    # 测试无效URL
    test_video_preview("invalid_url", "无效URL测试")
    
    # 测试空URL
    test_video_preview("", "空URL测试")
    
    # 测试不存在的流
    test_video_preview("rtmp://*************/live/nonexistent", "不存在的流测试")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "error":
            test_error_cases()
        else:
            # 测试单个URL
            test_video_preview(sys.argv[1], "自定义URL测试")
    else:
        # 运行完整测试
        test_multiple_formats() 
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import logging
import requests
from config import SRS_SERVER_URL, SRS_HTTP_PORT, SRS_WEBRTC_PORT

logger = logging.getLogger(__name__)
router = APIRouter()

class WebRTCStreamRequest(BaseModel):
    stream_name: str

class WebRTCStreamResponse(BaseModel):
    webrtc_url: str
    rtmp_url: str

@router.post("/webrtc/stream", response_model=WebRTCStreamResponse)
async def get_webrtc_stream(request: WebRTCStreamRequest):
    """获取WebRTC流地址（辅助接口）"""
    try:
        # 直接返回SRS的WebRTC流地址
        webrtc_url = f"webrtc://{SRS_SERVER_URL}:8000/live/{request.stream_name}"
        rtmp_url = f"rtmp://{SRS_SERVER_URL}:1935/live/{request.stream_name}"
        
        logger.info(f"返回WebRTC流地址: {webrtc_url}")
        
        return WebRTCStreamResponse(
            webrtc_url=webrtc_url,
            rtmp_url=rtmp_url
        )
            
    except Exception as e:
        logger.error(f"获取WebRTC流地址异常: {e}")
        raise HTTPException(status_code=500, detail="获取流地址失败")

# 保留简化的流地址获取接口作为辅助
class WebRTCPlayRequest(BaseModel):
    streamurl: str
    sdp: str

class WebRTCPlayResponse(BaseModel):
    code: int
    sdp: str

@router.post("/webrtc/play", response_model=WebRTCPlayResponse)
async def webrtc_play(request: WebRTCPlayRequest):
    """WebRTC播放代理接口 - 处理唯一流名称的WebRTC连接"""
    try:
        # 解析流URL，提取流名称
        stream_name = None
        if "webrtc://" in request.streamurl:
            # 格式: webrtc://172.20.10.3:8000/live/stream_name
            parts = request.streamurl.split("/")
            stream_name = parts[-1] if parts else "unknown"
        elif "rtmp://" in request.streamurl:
            # 格式: rtmp://172.20.10.3:1935/live/stream_name
            parts = request.streamurl.split("/")
            stream_name = parts[-1] if parts else "unknown"
        else:
            # 直接使用streamurl作为流名称
            stream_name = request.streamurl
        
        logger.info(f"处理WebRTC连接请求 - 流名称: {stream_name}")
        logger.info(f"原始streamurl: {request.streamurl}")
        
        # 构建SRS WebRTC API地址
        srs_url = f"http://{SRS_SERVER_URL}:{SRS_HTTP_PORT}/rtc/v1/play/"
        
        # 构建SRS期望的streamurl格式 - 使用正确的格式
        srs_streamurl = f"webrtc://{SRS_SERVER_URL}:{SRS_WEBRTC_PORT}/live/{stream_name}"
        
        payload = {
            "streamurl": srs_streamurl,
            "sdp": request.sdp
        }
        
        logger.info(f"转发WebRTC请求到SRS: {srs_url}")
        logger.info(f"SRS请求参数: {payload}")
        
        # 发送请求到SRS
        response = requests.post(srs_url, json=payload, timeout=10)
        
        logger.info(f"SRS响应状态码: {response.status_code}")
        logger.info(f"SRS响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                logger.info(f"SRS WebRTC响应成功: {data}")
                
                if data.get("code") == 0:
                    return WebRTCPlayResponse(
                        code=0,
                        sdp=data.get("sdp", "")
                    )
                else:
                    error_msg = data.get('msg', '未知错误')
                    logger.error(f"SRS返回错误码: {data.get('code')} - {error_msg}")
                    raise HTTPException(status_code=500, detail=f"SRS错误: {error_msg}")
            except ValueError as e:
                logger.error(f"SRS响应JSON解析失败: {e}")
                raise HTTPException(status_code=500, detail="SRS响应格式错误")
        else:
            logger.error(f"SRS WebRTC请求失败: {response.status_code} - {response.text}")
            raise HTTPException(status_code=500, detail=f"WebRTC协商失败: HTTP {response.status_code}")
            
    except requests.RequestException as e:
        logger.error(f"WebRTC代理请求异常: {e}")
        raise HTTPException(status_code=500, detail="WebRTC服务不可用")
    except Exception as e:
        logger.error(f"WebRTC代理异常: {e}")
        raise HTTPException(status_code=500, detail="WebRTC处理失败") 
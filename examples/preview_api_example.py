#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频预览API使用示例
"""
import requests
import json

def preview_video_example():
    """视频预览API使用示例"""
    
    # API端点
    url = "http://localhost:8000/api/video_url/preview"
    
    # 请求数据
    data = {
        "video_url": "rtmp://*************/live/test_stream"
    }
    
    # 发送请求
    try:
        response = requests.post(url, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 预览成功!")
            print(f"分辨率: {result['resolution']['width']} x {result['resolution']['height']}")
            print(f"帧率: {result['fps']}")
            print(f"图片大小: {len(result['preview_image'])} 字符")
            
            # 可以将base64图片保存到文件
            import base64
            with open("preview.jpg", "wb") as f:
                f.write(base64.b64decode(result['preview_image']))
            print("✅ 图片已保存为 preview.jpg")
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    preview_video_example() 
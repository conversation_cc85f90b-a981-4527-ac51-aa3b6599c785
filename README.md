# CV智能分析系统后端

## 项目简介
这是一个基于FastAPI的CV智能分析系统后端，支持视频和图像处理，集成了外部AI服务。

## 主要功能

### 1. 视频分析
- 支持视频文件上传分析
- 支持视频流URL分析
- 支持出入栏检测和圈内监控
- 自动推流到SRS服务器

### 2. 图像分析
- 支持图片文件上传分析
- 支持base64编码图片分析
- 圈内监控功能

### 3. 视频预览功能 (新增)
- **接口**: `POST /api/video_url/preview`
- **功能**: 获取视频流URL的第一帧预览图片
- **请求参数**:
  ```json
  {
    "video_url": "rtmp://example.com/live/stream"
  }
  ```
- **响应格式**:
  ```json
  {
    "success": true,
    "message": "视频预览获取成功",
    "preview_image": "base64编码的图片",
    "resolution": {
      "width": 1920,
      "height": 1080
    },
    "fps": 30.0
  }
  ```

## API接口列表

### 视频分析接口
- `POST /api/analysis/start` - 启动URL视频分析
- `POST /api/analysis/start-file` - 启动文件视频分析
- `POST /api/analysis/stop/{session_id}` - 停止视频分析
- `GET /api/analysis/status` - 获取分析状态

### 视频预览接口
- `POST /api/video_url/preview` - 获取视频流预览

### 系统状态接口
- `GET /health` - 健康检查
- `GET /api/system/status` - 系统状态

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置环境
复制并修改配置文件中的相关设置：
- SRS服务器地址
- AI服务地址
- 端口配置等

### 3. 运行服务
```bash
python run.py
```

### 4. 测试预览接口
```bash
python test_preview.py
```

## 技术栈
- FastAPI - Web框架
- OpenCV - 视频图像处理
- FFmpeg - 视频流处理
- SRS - 流媒体服务器
- Pydantic - 数据验证

## 注意事项
- 确保SRS服务器正在运行
- 确保AI服务地址可访问
- 视频流URL需要支持OpenCV读取
- 预览接口支持RTMP、RTSP、HTTP等协议 
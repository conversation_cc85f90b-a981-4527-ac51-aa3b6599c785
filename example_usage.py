#!/usr/bin/env python3
"""
示例：演示如何使用UploadFile直接推流功能
"""

import asyncio
import aiohttp
import aiofiles

async def upload_video_direct_stream():
    """演示直接从内存推流的功能"""
    
    # 模拟读取视频文件
    video_file_path = "sample_video.mp4"  # 请替换为实际的视频文件路径
    
    try:
        # 异步读取视频文件
        async with aiofiles.open(video_file_path, 'rb') as f:
            video_content = await f.read()
        
        print(f"✅ 读取视频文件成功，大小: {len(video_content)} 字节")
        
        # 准备API请求
        url = "http://localhost:8000/api/analysis/start-file"
        
        # 创建表单数据
        form_data = aiohttp.FormData()
        form_data.add_field('mode', 'passage_pig_count')
        form_data.add_field('direction', 'in')
        
        # 添加视频文件 - 直接从内存
        form_data.add_field(
            'file', 
            video_content,
            filename='video.mp4',
            content_type='video/mp4'
        )
        
        # 发送请求
        async with aiohttp.ClientSession() as session:
            print("🚀 开始上传并推流...")
            
            async with session.post(url, data=form_data) as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ 推流启动成功!")
                    print(f"会话ID: {result.get('session_id')}")
                    print(f"原始流: {result.get('stream_urls', {}).get('original')}")
                    print(f"处理流: {result.get('stream_urls', {}).get('processed')}")
                    
                    return result.get('session_id')
                else:
                    error_text = await response.text()
                    print(f"❌ 推流失败: {response.status}")
                    print(f"错误: {error_text}")
                    return None
                    
    except FileNotFoundError:
        print(f"❌ 视频文件不存在: {video_file_path}")
        return None
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return None

async def stop_stream(session_id: str):
    """停止推流"""
    if not session_id:
        return
        
    url = f"http://localhost:8000/api/analysis/stop/{session_id}"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 停止推流成功: {result.get('message')}")
                else:
                    print(f"❌ 停止推流失败: {response.status}")
    except Exception as e:
        print(f"❌ 停止推流异常: {e}")

async def main():
    """主函数"""
    print("=== UploadFile直接推流示例 ===")
    print()
    
    # 启动推流
    session_id = await upload_video_direct_stream()
    
    if session_id:
        print()
        print("⏳ 推流运行中，等待10秒...")
        await asyncio.sleep(10)
        
        print()
        print("🛑 停止推流...")
        await stop_stream(session_id)
    
    print()
    print("✅ 示例完成")

if __name__ == "__main__":
    # 安装依赖: pip install aiohttp aiofiles
    asyncio.run(main())

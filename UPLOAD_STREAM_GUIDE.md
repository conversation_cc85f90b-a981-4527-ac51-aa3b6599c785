# UploadFile直接推流功能指南

## 概述

本功能允许FastAPI接收到的UploadFile视频文件直接通过ffmpeg推流，无需先保存到本地文件系统。这样可以：

- 节省磁盘空间
- 提高处理速度
- 减少I/O操作
- 降低临时文件管理复杂度

## 技术实现

### 核心改动

1. **VideoProcessor.start_analysis()** 方法现在支持 `Union[str, bytes]` 类型的输入源
2. **VideoProcessor._push_stream()** 方法增加了对bytes类型输入的处理
3. **新增 _start_ffmpeg_stream_from_bytes()** 方法专门处理字节流推流
4. **API端点修改** 直接读取UploadFile内容到内存而不保存文件

### 工作流程

```
UploadFile -> 读取到内存(bytes) -> ffmpeg pipe -> RTMP推流 -> SRS服务器
```

## 使用方法

### API调用示例

```python
import aiohttp

async def upload_video_stream():
    url = "http://localhost:8000/api/analysis/start-file"
    
    form_data = aiohttp.FormData()
    form_data.add_field('mode', 'passage_pig_count')
    form_data.add_field('direction', 'in')
    
    with open('video.mp4', 'rb') as f:
        form_data.add_field('file', f, filename='video.mp4', content_type='video/mp4')
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, data=form_data) as response:
                result = await response.json()
                print(f"流URL: {result['stream_urls']}")
```

### curl示例

```bash
curl -X POST "http://localhost:8000/api/analysis/start-file" \
  -F "mode=passage_pig_count" \
  -F "direction=in" \
  -F "file=@video.mp4"
```

## 支持的输入类型

现在 `VideoProcessor.start_analysis()` 支持以下输入类型：

1. **URL字符串** (http://, https://, rtmp://, rtsp://)
   - 直接推流，不重新编码
   
2. **本地文件路径字符串**
   - 使用OpenCV逐帧读取并推流
   
3. **视频文件字节内容 (新增)**
   - 直接通过ffmpeg pipe推流

## 配置要求

### 依赖包
- ffmpeg-python
- aiohttp
- fastapi
- opencv-python

### 系统要求
- 安装ffmpeg命令行工具
- SRS流媒体服务器运行中

## 性能优化

### 内存使用
- 视频文件完全加载到内存中
- 适合中小型视频文件 (建议 < 100MB)
- 大文件可能导致内存压力

### 推流优化
- 使用 `vcodec='copy'` 避免重新编码
- `format='auto'` 自动检测视频格式
- `quiet=True` 减少ffmpeg日志输出

## 错误处理

### 常见错误

1. **内存不足**
   ```
   解决方案: 增加系统内存或使用文件模式
   ```

2. **ffmpeg推流失败**
   ```
   检查: SRS服务器状态、网络连接、视频格式
   ```

3. **视频格式不支持**
   ```
   确保视频格式被ffmpeg支持
   ```

## 测试

运行测试脚本：

```bash
python test_upload_stream.py
```

确保以下服务运行：
- FastAPI服务: `python -m uvicorn api.main:app --reload`
- SRS服务器
- AI分析服务

## 监控和日志

### 关键日志信息

```python
logger.info(f"会话ID {session_id}: 文件大小 {len(file_content)} 字节，直接从内存推流")
logger.info(f"FFmpeg从字节流推流到: {rtmp_url}")
```

### 性能监控

- 监控内存使用情况
- 检查ffmpeg进程状态
- 观察推流延迟

## 最佳实践

1. **文件大小限制**
   - 设置合理的MAX_FILE_SIZE
   - 对大文件使用传统文件模式

2. **错误恢复**
   - 实现重试机制
   - 优雅处理ffmpeg进程异常

3. **资源清理**
   - 确保ffmpeg进程正确终止
   - 及时释放内存资源

## 向后兼容性

- 原有的URL和文件路径模式继续支持
- API接口保持不变
- 现有客户端代码无需修改

import logging

import uvicorn

# # 添加项目根目录到Python路径
# project_root = Path(__file__).parent
# sys.path.insert(0, str(project_root))
from config import API_HOST, API_PORT, LOG_LEVEL

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    logger = logging.getLogger(__name__)
    logger.info(f"启动CV智能分析系统后端服务 - {API_HOST}:{API_PORT}")

    # 启动FastAPI应用
    uvicorn.run(
        "api.main:app",
        host=API_HOST,
        port=API_PORT,
        reload=True,
        log_level=LOG_LEVEL.lower(),
    )

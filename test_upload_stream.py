#!/usr/bin/env python3
"""
测试脚本：验证UploadFile直接推流功能
"""

import asyncio
import os

import aiohttp


async def test_upload_stream():
    """测试上传视频文件并直接推流"""

    # 检查是否有测试视频文件
    test_video_path = "test_video.mp4"  # 请替换为实际的测试视频文件路径

    if not os.path.exists(test_video_path):
        print(f"错误: 测试视频文件 {test_video_path} 不存在")
        print("请提供一个测试视频文件，或修改 test_video_path 变量")
        return

    # API端点
    api_url = "http://localhost:8000/api/analysis/start-file"

    # 准备表单数据
    form_data = aiohttp.FormData()
    form_data.add_field("mode", "passage_pig_count")
    form_data.add_field("direction", "in")

    # 添加视频文件
    with open(test_video_path, "rb") as f:
        form_data.add_field(
            "file", f, filename="test_video.mp4", content_type="video/mp4"
        )

        try:
            async with aiohttp.ClientSession() as session:
                print(f"正在上传视频文件: {test_video_path}")
                print("测试直接从内存推流功能...")

                async with session.post(api_url, data=form_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        print("✅ 上传成功!")
                        print(f"会话ID: {result.get('session_id')}")
                        print(f"流URL: {result.get('stream_urls')}")
                        print("✅ 直接从内存推流功能正常工作!")
                    else:
                        error_text = await response.text()
                        print(f"❌ 上传失败: HTTP {response.status}")
                        print(f"错误信息: {error_text}")

        except Exception as e:
            print(f"❌ 请求异常: {e}")


def main():
    """主函数"""
    print("=== UploadFile直接推流功能测试 ===")
    print()

    # 检查服务是否运行
    print("请确保以下服务正在运行:")
    print("1. FastAPI服务 (python -m uvicorn api.main:app --reload)")
    print("2. SRS流媒体服务器")
    print("3. AI服务")
    print()

    # 运行测试
    asyncio.run(test_upload_stream())


if __name__ == "__main__":
    main()

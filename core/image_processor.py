import base64
import io
import json
import logging
import time
from typing import Dict, List, Optional

import cv2
import numpy as np
import requests
from PIL import Image

from core.models import ImageAiServiceRequest

logger = logging.getLogger(__name__)


class ImageProcessor:
    """
    一个用于处理静态图片的处理器。
    支持从URL或字节流加载图片，并提供"出入栏"和"圈内"两种处理模式。
    """

    def __init__(self, ai_func):
        # 对于静态图片处理，不需要活跃流、处理线程或停止标志
        self._ai_func = ai_func

    async def draw_detection_box(
        self, frame: np.ndarray, detection: dict, color: tuple
    ) -> None:
        """仅绘制边界框和标签"""
        x1, y1, w, h = map(int, detection["bbox"])
        class_name = detection["type"]
        confidence = detection["score"]
        cv2.rectangle(frame, (x1, y1), (x1 + w, y1 + h), color, 3)  # 边框厚度加大
        label = f"{class_name}: {confidence}"
        (text_width, text_height), _ = cv2.getTextSize(
            label, cv2.FONT_HERSHEY_SIMPLEX, 1.0, 3
        )  # 字体和厚度加大
        cv2.rectangle(
            frame, (x1, y1 - text_height - 16), (x1 + text_width, y1), color, -1
        )
        cv2.putText(
            frame, label, (x1, y1 - 8), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 3
        )

    async def draw_info_text(
        self, frame: np.ndarray, pig_num: int, pig_weights: float
    ) -> None:
        """仅绘制猪数量和重量信息（一次）"""
        # 在左上角直接绘制猪的数量和重量信息
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1.0  # 调整字体大小
        thickness = 2  # 调整线条粗细
        text_color = (0, 255, 0)  # 绿色

        # 计算文本宽度以自适应背景框
        pig_count_text = f"Pig Count: {abs(pig_num) if pig_num is not None else 0}"
        pig_weight_text = f"Pig Weight: {pig_weights if pig_weights is not None else 0}"
        combined_text = f"{pig_count_text} {pig_weight_text}kg"

        (text_width, text_height), _ = cv2.getTextSize(
            combined_text, font, font_scale, thickness
        )

        # 显示在视频顶部，但不遮挡日期
        top_margin = 5  # 顶部边距
        rect_x1, rect_y1 = 10, top_margin
        rect_x2, rect_y2 = rect_x1 + text_width + 20, rect_y1 + text_height + 10

        # 绘制背景框
        cv2.rectangle(frame, (rect_x1, rect_y1), (rect_x2, rect_y2), text_color, 2)

        # 绘制猪数量和重量在同一行
        cv2.putText(
            frame,
            combined_text,
            (rect_x1 + 10, rect_y1 + text_height),
            font,
            font_scale,
            text_color,
            thickness,
        )

    async def process_image(
        self,
        session_id: str,
        image_base64: str,
        ref_coord: Optional[str],
        resolution: Optional[str],
        mode: Optional[str] = None,
        direction: Optional[str] = None,
        areas: Optional[List] = None,
        is_weight=False,
    ) -> Dict:
        """
        根据指定模式处理图片，并将处理后的图片以Base64编码返回。

        Args:
            session_id (str): 处理会话的唯一ID。
            mode (str): 处理模式，例如 "出入栏" 或 "圈内"。
            input_data (Union[str, bytes]): 图片输入数据。可以是图片URL（字符串）或原始图片字节。
            direction (Optional[str]): "出入栏"模式的方向（例如："上"，"下"，"左"，"右"）。
            areas (Optional[List]): "圈内"模式的区域定义列表。
            is_weight (bool): 是否为重量检测。

        Returns:
            Dict: 包含成功状态和Base64编码处理后图片的字典，或错误信息。
        """
        areas_new = []
        areas_final = []
        inde = 1
        if areas is not None:
            ref_coord = json.loads(ref_coord)
            resolution = json.loads(resolution)
            sigma = resolution[-1] / ref_coord[-1]  # TODO check
            for area in areas:
                for a in area["area"]:
                    areas_new.append(
                        [int(a[0] * sigma / inde), int(a[1] * sigma / inde)]
                    )
                areas_final.append({"area": areas_new, "area_id": area["area_id"]})
        try:
            req1 = ImageAiServiceRequest(
                seqid=session_id,
                timestamp=time.time(),
                image=image_base64,
                area_monitor=areas_final,
                is_weight=False,
            )
            req2 = ImageAiServiceRequest(
                seqid=session_id,
                timestamp=time.time(),
                image=image_base64,
                area_monitor=areas_final,
                is_weight=True,
            )
            resp1 = await self._ai_func(req1, "image_detection")
            resp2 = await self._ai_func(req2, "image_weight")
            # resp2 = {'code': '1'}
            if resp1["code"] == "10000":
                detections = resp1["data"]
                pig_num = resp1["pignum"]
            else:
                detections = [{"bbox": [0, 0, 0, 0], "score": 0, "type": "pig"}]
                pig_num = 0
            if resp2["code"] == "10000":
                pig_weights = resp2["data"][0]["weight"]
            else:
                pig_weights = 0
            img_bytes = base64.b64decode(image_base64)
            frame = cv2.imdecode(np.frombuffer(img_bytes, np.uint8), cv2.IMREAD_COLOR)
            for detection in detections:
                await self.draw_detection_box(frame, detection, color=(0, 255, 0))
            await self.draw_info_text(frame, pig_num, pig_weights)
            # result = await self.draw_detection(frame, resp1['data'][0], (0,255,0), 1, resp2['data']['weight'])
            # TODO change draw_detection
            # test_res = f"data:image/png;base64,{base64.b64encode(img_bytes).decode('utf-8')}"

            _, encoded_image = cv2.imencode(".png", frame)
            base64_encoded_frame = base64.b64encode(encoded_image.tobytes()).decode(
                "utf-8"
            )
            res = f"data:image/png;base64,{base64_encoded_frame}"

            return res

        except ValueError as e:
            logger.error(f"会话 {session_id} 的图片处理失败（输入错误）: {e}")
            return {"success": False, "message": str(e)}
        except Exception as e:
            logger.error(
                f"会话 {session_id} 的图片处理发生意外错误: {e}", exc_info=True
            )
            return {"success": False, "message": f"发生内部错误: {e}"}

    def _load_image_from_url(self, url: str) -> Optional[np.ndarray]:
        """从URL加载图片，并将其作为OpenCV numpy数组返回。"""
        try:
            response = requests.get(url, stream=True, timeout=10)  # 增加超时
            response.raise_for_status()  # 对4xx/5xx响应抛出HTTPError

            image_bytes = io.BytesIO(response.content)
            # 使用PIL打开，然后转换为OpenCV格式（BGR）
            pil_image = Image.open(image_bytes).convert("RGB")  # 确保有3个通道
            opencv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            logger.info(f"从URL加载图片成功: {url}")
            return opencv_image
        except requests.exceptions.RequestException as e:
            logger.error(f"从URL {url} 下载图片失败: {e}")
            return None
        except IOError:
            logger.error(f"无法从URL {url} 打开或处理图片。是否为无效图片格式？")
            return None
        except Exception as e:
            logger.error(f"从URL {url} 加载图片时发生意外错误: {e}")
            return None

    def _load_image_from_bytes(self, image_bytes: bytes) -> Optional[np.ndarray]:
        """从字节加载图片，并将其作为OpenCV numpy数组返回。"""
        try:
            image_stream = io.BytesIO(image_bytes)
            # 使用PIL打开，然后转换为OpenCV格式（BGR）
            pil_image = Image.open(image_stream).convert("RGB")  # 确保有3个通道
            opencv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            logger.info("从字节加载图片成功。")
            return opencv_image
        except IOError:
            logger.error("无法从字节打开或处理图片。是否为无效图片格式？")
            return None
        except Exception as e:
            logger.error(f"从字节加载图片时发生意外错误: {e}")
            return None

    def _process_single_image(
        self,
        image: np.ndarray,
        mode: str,
        direction: Optional[str],
        areas: Optional[List],
    ) -> np.ndarray:
        """
        根据指定模式处理单个图片（OpenCV numpy数组）。
        """
        try:
            processed_image = image.copy()

            if mode == "passage_pig_count":
                return self._process_line_crossing(processed_image, direction)
            elif mode == "圈内":
                return self._process_area_monitoring(processed_image, areas)
            else:
                # 如果模式未识别，则返回原始图片并添加提示信息
                cv2.putText(
                    processed_image,
                    "未知模式",
                    (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    1,
                    (0, 0, 255),
                    2,
                )
                logger.warning(f"未知处理模式: {mode}。返回原始图片。")
                return processed_image
        except Exception as e:
            logger.error(f"_process_single_image 中的图片处理失败: {e}")
            return image  # 错误时返回原始图片

    def _process_line_crossing(self, image: np.ndarray, direction: str) -> np.ndarray:
        """
        在静态图片上处理出入栏检测。
        """
        h, w = image.shape[:2]
        line_color = (0, 255, 0)  # 绿色
        text_color = (0, 255, 0)  # 绿色
        line_thickness = 2
        font_scale = 1
        font_thickness = 2

        if direction == "du":
            cv2.line(image, (0, h // 2), (w, h // 2), line_color, line_thickness)
        elif direction == "ud":
            cv2.line(image, (0, h // 2), (w, h // 2), line_color, line_thickness)
        elif direction == "rl":
            cv2.line(image, (w // 2, 0), (w // 2, h), line_color, line_thickness)
        elif direction == "lr":
            cv2.line(image, (w // 2, 0), (w // 2, h), line_color, line_thickness)
        else:
            cv2.putText(
                image,
                "无效方向",
                (10, 60),
                cv2.FONT_HERSHEY_SIMPLEX,
                font_scale,
                (0, 0, 255),
                font_thickness,
            )
            logger.warning(f"出入栏的无效方向: {direction}")
            return image

        cv2.putText(
            image,
            f"出入栏: {direction}",
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            font_scale,
            text_color,
            font_thickness,
        )

        return image

    def _process_area_monitoring(self, image: np.ndarray, areas: List) -> np.ndarray:
        """
        在静态图片上处理圈内检测。
        """
        if not areas:
            cv2.putText(
                image,
                "未定义区域",
                (10, 60),
                cv2.FONT_HERSHEY_SIMPLEX,
                1,
                (0, 0, 255),
                2,
            )
            return image

        polyline_color = (0, 255, 0)  # 绿色
        fill_color = (0, 255, 0)  # 绿色
        fill_alpha = 0.2  # 半透明填充
        text_color = (255, 255, 255)  # 白色
        line_thickness = 2
        font_scale = 0.8
        font_thickness = 2

        overlay = image.copy()
        for area in areas:
            if (
                "area" not in area
                or not isinstance(area["area"], list)
                or len(area["area"]) < 3
            ):
                logger.warning(f"无效区域定义或点数少于3: {area}。跳过。")
                continue

            points = np.array(area["area"], np.int32)
            points = points.reshape((-1, 1, 2))

            # 绘制填充多边形（半透明）
            cv2.fillPoly(overlay, [points], fill_color)

            # 绘制多边形轮廓
            cv2.polylines(image, [points], True, polyline_color, line_thickness)

            # 添加区域ID
            # 计算区域的中心点作为文本位置
            if points.size > 0:  # 确保points不是空的
                center_x = int(np.mean([p[0][0] for p in points]))
                center_y = int(np.mean([p[0][1] for p in points]))

                cv2.putText(
                    image,
                    f"区域 {area.get('area_id', 'N/A')}",
                    (center_x, center_y),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    font_scale,
                    text_color,
                    font_thickness,
                )
            else:
                logger.warning(
                    f"计算区域 {area.get('area_id', 'N/A')} 中心点失败，跳过文本绘制。"
                )

        # 将半透明叠加层与原始图像混合
        image = cv2.addWeighted(overlay, fill_alpha, image, 1 - fill_alpha, 0)

        cv2.putText(
            image,
            f"区域监测: {len(areas)} 个区域",
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            1,
            polyline_color,
            line_thickness,
        )

        return image

from enum import Enum
from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, Field


class DetectionMode(str, Enum):
    LINE_CROSSING = "passage_pig_count"
    AREA_VIDEO = "inbar_video"
    AREA_IMAGE = "inbar_image"


class Direction(str, Enum):
    UP = "du"
    DOWN = "ud"
    LEFT = "rl"
    RIGHT = "lr"


class InputMode(str, Enum):
    FILE = "file"
    URL = "url"


class AreaPoint(BaseModel):
    area: List[List[int]] = Field(..., description="多边形区域坐标点列表")
    area_id: str = Field(..., description="区域ID")


class AnalysisRequest(BaseModel):
    mode: DetectionMode = Field(..., description="检测模式")
    input_mode: InputMode = Field(..., description="输入模式")
    video_url: Optional[str] = Field(None, description="视频URL")
    picture_url: Optional[str] = Field(None, description="图片URL")
    ref_coord: Optional[List] = Field(..., description="画布分辨率")
    resolution: Optional[List] = Field(..., description="视频分辨率")
    direction: Optional[Direction] = Field(None, description="检测方向(出入栏模式)")
    area_monitor: Optional[List[AreaPoint]] = Field(
        None, description="监控区域(圈内模式)"
    )


class AnalysisResponse(BaseModel):
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    stream_urls: Optional[Dict[str, str]] = Field(None, description="WebRTC流URL")
    session_id: Optional[str] = Field(None, description="会话ID")


class StreamInfo(BaseModel):
    stream_id: str = Field(..., description="流ID")
    rtmp_url: str = Field(..., description="RTMP推流地址")
    webrtc_url: str = Field(..., description="WebRTC播放地址")
    status: Literal["active", "inactive", "error"] = Field(..., description="流状态")


class SystemStatus(BaseModel):
    status: Literal["running", "stopped", "error"] = Field(..., description="系统状态")
    active_streams: int = Field(..., description="活跃流数量")
    cpu_usage: float = Field(..., description="CPU使用率")
    memory_usage: float = Field(..., description="内存使用率")
    gpu_usage: Optional[float] = Field(None, description="GPU使用率")


class VideoAiServiceRequest(BaseModel):
    stream_id: str = Field(..., description="<UNK>ID")
    input_url: str = Field(None, description="RTSP \ RTMP视频流")
    models: List = Field(..., description="passage_pig_count 、 testmodel")
    direction: Optional[str] = Field(None, description="检测方向(出入栏模式)")
    area_monitor: Optional[List[AreaPoint]] = Field(
        None, description="监控区域(圈内模式)"
    )


class ImageAiServiceRequest(BaseModel):
    seqid: str = Field(..., description="<UNK>ID")
    timestamp: float = Field(..., description="time.time()")
    image: str = Field(..., description="image_base64")
    config: Optional[Dict[str, Any]] = Field(None, description="<UNK>")
    area_monitor: Optional[List[AreaPoint]] = Field(
        None, description="监控区域(圈内模式)"
    )
    is_weight: bool = Field(..., description="是否估重")


class VideoPreviewRequest(BaseModel):
    video_url: str = Field(..., description="视频流URL")


class VideoPreviewResponse(BaseModel):
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    preview_image: str = Field(..., description="第一帧图片的base64编码")
    resolution: Dict[str, int] = Field(
        ..., description="视频分辨率 {width: int, height: int}"
    )
    fps: Optional[float] = Field(None, description="视频帧率")

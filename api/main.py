import asyncio
import base64
import json
import logging
import os
import uuid
from typing import Any, Dict, Optional, Union

import aiohttp
import cv2
import requests
from fastapi import FastAPI, File, Form, HTTPException, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse

from api.webrtc import router as webrtc_router
from config import (
    API_HOST,
    API_PORT,
    CORS_ORIGINS,
    MAX_FILE_SIZE,
    UPLOAD_DIR,
    AIService_API_HOST,
    AIService_API_Port_Stream,
)
from core.image_processor import ImageProcessor  # 确保已从正确的路径导入

# 假设这些模型和配置存在于 core/models.py 和 config.py
# 如果这些文件没有提供，请确保它们存在并包含必要的定义。
from core.models import (
    AnalysisRequest,
    AnalysisResponse,
    ImageAiServiceRequest,
    SystemStatus,
    VideoAiServiceRequest,
    VideoPreviewRequest,
    VideoPreviewResponse,
)
from core.srs_client import SR<PERSON>lient
from core.video_processor import VideoProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建应用实例
app = FastAPI(title="CV智能分析系统API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含WebRTC路由
app.include_router(webrtc_router, prefix="/api")


# --- request_from_ai_service ---
async def request_from_ai_service(
    request_data: Union[VideoAiServiceRequest, ImageAiServiceRequest], service_type: str
) -> Dict[str, Any]:
    AIService_API_HOST = "http://*************:8999"
    if service_type == "video":
        url = f"{AIService_API_HOST}/stream/start"
    elif service_type == "image_detection":
        # url = f"{AIService_API_HOST}/picture/pig_weight_estimation_realtime"
        url = "http://*************:9002/picture/pig_count_detection"
    elif service_type == "image_weight":
        url = "http://*************:9003/picture/pig_weight_estimation_realtime"
    else:
        raise ValueError(
            f"不支持的 AI 服务类型: {service_type}. 必须是 'video' 或 'image'."
        )
    try:
        async with aiohttp.ClientSession() as session:
            request_data = request_data.dict(exclude_none=True)
            payload = (
                request_data.model_dump()
                if hasattr(request_data, "model_dump")
                else request_data
            )
            async with session.post(url, json=payload, timeout=120) as resp:
                text = await resp.text()
                try:
                    return json.loads(text)
                except Exception:
                    return {"error": text}
    except asyncio.TimeoutError:
        raise HTTPException(status_code=504, detail=f"AI服务响应超时: {url}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI服务连接失败: {e}")


# --- END request_from_ai_service ---

# 初始化组件
video_processor = VideoProcessor(request_from_ai_service)
image_processor = ImageProcessor(request_from_ai_service)
srs_client = SRSClient()


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("CV智能分析系统启动")
    if not srs_client.check_server_status():
        logger.warning("SRS服务器连接失败，请确保SRS服务正在运行")
    os.makedirs(UPLOAD_DIR, exist_ok=True)


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("CV智能分析系统关闭")
    # 停止所有活跃的视频分析流
    active_streams = video_processor.get_active_streams()
    for session_id in active_streams.keys():
        video_processor.stop_analysis(session_id)


@app.get("/")
async def root():
    """根路径"""
    return {"message": "CV智能分析系统API", "status": "running"}


@app.get("/health")
async def health_check():
    """健康检查"""
    srs_status = srs_client.check_server_status()
    return {
        "status": "healthy",
        "srs_server": "connected" if srs_status else "disconnected",
        "active_video_streams": len(video_processor.get_active_streams()),
    }


@app.post("/api/video_url/preview", response_model=VideoPreviewResponse)
async def preview_video_url(request: VideoPreviewRequest):
    """获取视频流URL的第一帧预览图片"""
    cap = None
    try:
        video_url = request.video_url
        logger.info(f"接收到视频预览请求: {video_url}")

        # 验证URL格式
        if not video_url or not isinstance(video_url, str):
            raise HTTPException(status_code=400, detail="无效的视频URL")

        # 设置OpenCV超时和重试
        cap = cv2.VideoCapture(video_url)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲区大小

        # 尝试打开视频流，最多重试3次
        for attempt in range(3):
            if cap.isOpened():
                break
            await asyncio.sleep(1)
            cap = cv2.VideoCapture(video_url)

        if not cap.isOpened():
            raise HTTPException(
                status_code=400, detail="无法打开视频流，请检查URL是否正确"
            )

        # 读取第一帧，设置超时
        ret, frame = cap.read()
        if not ret or frame is None:
            raise HTTPException(
                status_code=400, detail="无法读取视频帧，请检查视频流是否正常"
            )

        # 获取视频信息
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)

        # 验证分辨率
        if width <= 0 or height <= 0:
            raise HTTPException(status_code=400, detail="无法获取有效的视频分辨率")

        # 将帧转换为JPEG格式的base64，优化图片质量
        encode_params = [cv2.IMWRITE_JPEG_QUALITY, 85, cv2.IMWRITE_JPEG_OPTIMIZE, 1]
        _, buffer = cv2.imencode(".webp", frame, [cv2.IMWRITE_WEBP_QUALITY, 85])
        image_base64 = (
            f"data:image/png;base64,{base64.b64encode(buffer).decode('utf-8')}"
        )

        logger.info(
            f"视频预览成功: {width}x{height}, FPS: {fps}, 图片大小: {len(image_base64)}字符"
        )

        return VideoPreviewResponse(
            success=True,
            message="视频预览获取成功",
            preview_image=image_base64,
            resolution={"width": width, "height": height},
            fps=fps,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"视频预览异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"视频预览失败: {str(e)}")
    finally:
        # 确保资源被释放
        if cap is not None:
            cap.release()


@app.post("/api/analysis/start", response_model=AnalysisResponse)
async def start_analysis(request: AnalysisRequest):
    """启动分析 (URL/Stream)"""
    session_id = str(uuid.uuid4())
    logger.info(
        f"接收到分析请求。会话ID: {session_id}, 模式: {request.mode}, 输入模式: {request.input_mode}"
    )

    try:
        # 验证请求参数
        if request.mode == "passage_pig_count" and not request.direction:
            raise HTTPException(status_code=400, detail="出入栏模式需要指定检测方向")

        if request.mode in ["inbar_video", "inbar_image"] and not request.area_monitor:
            raise HTTPException(status_code=400, detail="圈内模式需要指定监控区域")

        if request.input_mode != "url":
            raise HTTPException(
                status_code=400,
                detail="此接口仅支持URL模式。文件模式请使用 /api/analysis/start-file 接口。",
            )

        if not request.video_url:
            raise HTTPException(status_code=400, detail="URL模式需要提供视频URL")

        input_source = request.video_url

        if request.mode == "inbar_image":
            raise HTTPException(
                status_code=400, detail="仅支持视频url流地址，图片请走file"
            )
            # # 使用 ImageProcessor 处理图片
            # logger.info(f"会话ID {session_id}: 模式为 '圈内（图片）'，使用 ImageProcessor。")
            # image_result = image_processor.process_image(
            #     session_id=session_id,
            #     mode="圈内",  # ImageProcessor 内部处理模式简化为 "圈内"
            #     input_data=input_source,  # URL作为input_data
            #     areas=request.area_monitor
            # )
            # if image_result["success"]:
            #     # 针对图片处理返回特定的JSON响应
            #     return JSONResponse(content={
            #         "success": True,
            #         "message": "图片分析处理成功",
            #         "session_id": image_result["session_id"],
            #         "processed_image_base64": image_result["processed_image_base64"]
            #     })
            # else:
            #     raise HTTPException(status_code=500, detail=image_result["message"])
        else:
            # 使用 VideoProcessor 处理视频 url

            logger.info(
                f"会话ID {session_id}: 模式为 '{request.mode}'，使用 VideoProcessor。"
            )
            video_result = await video_processor.start_analysis(
                session_id=session_id,
                mode=request.mode,
                input_source=input_source,
                ref_coord=request.ref_coord,
                resolution=request.resolution,
                direction=request.direction,
                areas=request.area_monitor,
            )
            if video_result["success"]:
                return AnalysisResponse(
                    success=True,
                    message="视频分析启动成功",
                    stream_urls=video_result["stream_urls"],
                    session_id=session_id,
                )
            else:
                raise HTTPException(status_code=500, detail=video_result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动分析异常 (URL): {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"启动分析失败: {str(e)}")


@app.post("/api/analysis/start-file")
async def start_analysis_with_file(
    mode: str = Form(...),
    direction: Optional[str] = Form(None),
    ref_coord: Optional[str] = Form(None),
    resolution: Optional[str] = Form(None),
    area_monitor: Optional[str] = Form(None),
    file: UploadFile = File(...),
):
    """通过文件上传启动分析"""
    session_id = str(uuid.uuid4())
    logger.info(
        f"接收到文件上传分析请求。会话ID: {session_id}, 模式: {mode}, 文件名: {file.filename}"
    )

    try:
        # 验证文件大小
        if file.size > MAX_FILE_SIZE * 1024 * 1024:
            raise HTTPException(
                status_code=400, detail=f"文件大小超过限制({MAX_FILE_SIZE}MB)"
            )

        # 解析区域信息
        areas = None
        if area_monitor:
            try:
                areas = json.loads(area_monitor)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="区域信息格式错误")
            if not isinstance(areas, list):  # 确保解析后是列表
                raise HTTPException(
                    status_code=400, detail="区域信息不是有效的JSON数组"
                )

        if mode == "inbar_image":
            logger.info(
                f"会话ID {session_id}: 模式为 '圈内（图片）'，使用 ImageProcessor 处理上传文件。"
            )
            contents = await file.read()
            image_result = await image_processor.process_image(
                session_id=session_id,
                image_base64=(
                    base64.b64encode(contents).decode("utf-8")
                    if isinstance(contents, bytes)
                    else contents
                ),
                ref_coord=ref_coord,
                resolution=resolution,
                areas=areas,
                is_weight=False,
            )
            if image_result:  # .get("code") == "10000":
                return JSONResponse(
                    content={
                        "success": True,
                        "message": "图片分析处理成功",
                        "session_id": session_id,
                        "ai_result": image_result,  # todo image_result as...
                    }
                )
            else:
                raise HTTPException(
                    status_code=500,
                    detail=image_result.get("message", "AI图片服务失败"),
                )
        else:
            # 使用 VideoProcessor 处理视频文件
            logger.info(
                f"会话ID {session_id}: 模式为 '{mode}'，使用 VideoProcessor 处理上传文件。"
            )
            # 保存文件
            file_path = os.path.join(UPLOAD_DIR, f"{uuid.uuid4()}_{file.filename}")
            # 确保文件指针在开头
            await file.seek(0)
            with open(file_path, "wb") as f:
                content = await file.read()
                f.write(content)

            video_result = await video_processor.start_analysis(
                session_id=session_id,
                mode=mode,
                input_source=file_path,
                ref_coord=ref_coord,
                resolution=resolution,
                direction=direction,
                areas=areas,
            )
            if video_result["success"]:
                return AnalysisResponse(
                    success=True,
                    message="视频分析启动成功",
                    stream_urls=video_result["stream_urls"],
                    session_id=session_id,
                )
            else:
                raise HTTPException(status_code=500, detail=video_result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动分析异常 (文件): {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"启动分析失败: {str(e)}")


@app.post("/api/analysis/stop/{session_id}")
async def stop_analysis(session_id: str):
    """停止视频分析 (图片处理不需要停止，因为是单次操作)"""
    try:
        # 这里只停止视频流，因为图片处理是同步的，完成后就返回结果了
        success = await video_processor.stop_analysis(session_id)
        ai_service_stop = requests.post(
            f"{AIService_API_HOST}:{AIService_API_Port_Stream}/stream/stop/{session_id}"
        )
        if success and ai_service_stop:
            return {"success": True, "message": "分析停止成功, 远端停止成功"}

        # clear uploads file
        file_path = os.path.join(UPLOAD_DIR, f"/{session_id}.mp4")
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"File '{file_path}' deleted successfully.")
            except OSError as e:
                print(f"Error deleting file '{file_path}': {e}")
        else:
            # 如果不是视频会话，可能返回404或成功，这里返回404表示没有找到活跃的视频流
            raise HTTPException(
                status_code=404, detail="会话不存在或不是一个活跃的视频分析会话"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止分析异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"停止分析失败: {str(e)}")


@app.get("/api/analysis/status")
async def get_analysis_status():
    """获取视频分析状态"""
    try:
        active_streams = video_processor.get_active_streams()
        return {
            "active_video_sessions": len(active_streams),
            "sessions": active_streams,
        }
    except Exception as e:
        logger.error(f"获取状态异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@app.get("/api/system/status", response_model=SystemStatus)
async def get_system_status():
    """获取系统状态"""
    try:
        import psutil  # 确保 psutil 已安装 (pip install psutil)

        active_video_streams = video_processor.get_active_streams()
        cpu_usage = psutil.cpu_percent(interval=1)
        memory_usage = psutil.virtual_memory().percent

        return SystemStatus(
            status="running" if len(active_video_streams) > 0 else "idle",
            active_streams=len(active_video_streams),
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
        )
    except Exception as e:
        logger.error(f"获取系统状态异常: {e}", exc_info=True)
        return SystemStatus(
            status="error", active_streams=0, cpu_usage=0.0, memory_usage=0.0
        )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host=API_HOST, port=API_PORT)

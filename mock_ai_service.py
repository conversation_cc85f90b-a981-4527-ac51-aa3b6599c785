import aiohttp
import asyncio
import time
import base64

def image_to_base64() -> str:
    """
    读取本地图片文件并转换为 base64 编码的字符串。

    :param image_path: 图片文件的本地路径
    :return: base64 编码的字符串
    """
    image_path = '/Users/<USER>/Documents/Work-AI/pigsty-cv-projec/Screenshot 2025-06-27 at 18.13.10.jpg'
    with open(image_path, "rb") as image_file:
        image_bytes = image_file.read()
        encoded_str = base64.b64encode(image_bytes).decode('utf-8')
        return encoded_str


async def start_stream(session, stream_id):
    data = {
        "seqid": stream_id,
        "timestamp": time.time(),
        'image': image_to_base64(),
        'area_monitor': [
            {"area": [[307, 271], [501, 172], [698, 166], [888, 230],
                      [966, 431], [903, 743], [701, 835], [481, 823],
                      [349, 753], [282, 490]],
             "area_id": "1"}
        ],
        "is_weight": False
        # 'direction': 'du',
        # "callback_url": 'http://localhost:8666/alerts',
        # "callback_interval": 10
    }
    url = 'http://*************:9003/picture/pig_weight_estimation_realtime'
    #  http://*************:9002/picture/pig_count_detection
    async with session.post('http://*************:9002/picture/pig_count_detection', json=data) as response:
        result = await response.text()
        print(f"Stream '{stream_id}' started: {result}")


async def main():
    async with aiohttp.ClientSession() as session:
        await start_stream(session, "03eaadeed96642d3ad8e43780945cf3b")


if __name__ == "__main__":
    asyncio.run(main())

# async def start_stream(session, stream_id, input_url, models):
#     data = {
#         "stream_id": stream_id,
#         "input_url": input_url,
#         "models": models,
#         # 'enable_stream': True,
#         'area_monitor': [
#             {"area": [[307, 271], [501, 172], [698, 166], [888, 230],
#                       [966, 431], [903, 743], [701, 835], [481, 823],
#                       [349, 753], [282, 490]],
#              "area_id": "1"}
#         ],
#         # 'direction': 'du',
#         # "callback_url": 'http://localhost:8666/alerts',
#         # "callback_interval": 10
#     }
#
#     async with session.post('http://*************:8999/stream/start', json=data) as response:
#         result = await response.text()
#         print(f"Stream '{stream_id}' started: {result}")
#
#
# async def main():
#     async with aiohttp.ClientSession() as session:
#         await start_stream(session, "03eaadeed96642d3ad8e43780945cf3b", "rtmp://*************/live/livestream", ["testmodel"])
#
#
# if __name__ == "__main__":
#     asyncio.run(main())
import logging
from typing import Dict, Optional

import requests

from config import SRS_API_PORT, SRS_HTTP_PORT, SRS_RTMP_PORT, SRS_SERVER_URL

logger = logging.getLogger(__name__)


class SRSClient:
    def __init__(self):
        self.base_url = f"{SRS_SERVER_URL}:{SRS_API_PORT}"
        self.rtmp_port = SRS_RTMP_PORT
        self.http_port = SRS_HTTP_PORT

    def create_stream(self, stream_id: str) -> Optional[Dict]:
        """创建流"""
        try:
            url = f"{self.base_url}/api/v1/streams"
            data = {"stream_id": stream_id}
            response = requests.post(url, json=data, timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"创建流失败: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            logger.error(f"SRS创建流异常: {e}")
            return None

    def get_stream_info(self, stream_id: str) -> Optional[Dict]:
        """获取流信息"""
        try:
            url = f"{self.base_url}/api/v1/streams/{stream_id}"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(
                    f"获取流信息失败: {response.status_code} - {response.text}"
                )
                return None
        except Exception as e:
            logger.error(f"SRS获取流信息异常: {e}")
            return None

    def delete_stream(self, stream_id: str) -> bool:
        """删除流"""
        try:
            url = f"{self.base_url}/api/v1/streams/{stream_id}"
            response = requests.delete(url, timeout=5)
            if response.status_code == 200:
                logger.info(f"删除流成功: {stream_id}")
                return True
            else:
                logger.error(f"删除流失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            logger.error(f"SRS删除流异常: {e}")
            return False

    def get_rtmp_url(self, stream_id: str) -> str:
        """获取RTMP推流地址"""
        return f"rtmp://192.168.208.4:{self.rtmp_port}/live/{stream_id}"

    def get_webrtc_url(self, stream_id: str) -> str:
        """获取WebRTC播放地址"""
        return f"webrtc://192.168.208.4:{self.http_port}/live/{stream_id}"

    def check_server_status(self) -> bool:
        """检查SRS服务器状态"""
        try:
            url = f"{self.base_url}/api/v1/versions"
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"SRS服务器连接失败: {e}")
            return False
